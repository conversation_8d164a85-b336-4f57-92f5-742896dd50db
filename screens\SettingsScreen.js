import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Modal,
  TouchableOpacity,
  Alert
} from "react-native";
import { ProgressBar, Avatar } from "react-native-paper";
import { LinearGradient } from 'expo-linear-gradient';
import { selectQuery, updateQuery, executeQuery } from "../src/controller";
import * as FileSystem from 'expo-file-system';
import * as DocumentPicker from 'expo-document-picker';
import * as Crypto from 'expo-crypto';
import { MaterialIcons } from '@expo/vector-icons';

const SettingsScreen = ({ navigation }) => {
  const [configs, setConfigs] = useState([]);
  const [selectedConfig, setSelectedConfig] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [expandedConfigId, setExpandedConfigId] = useState(null);
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    phone: '',
    message: ''
  });
  const [userData, setUserData] = useState({
    name: '@appsinppuser',
    email: '<EMAIL>',
    phone: '+91 8129999999'
  });

  useEffect(() => {
    const fetchConfigs = async () => {
      const data = await selectQuery('config', {}, '*', { orderBy: 'title' });
      if (data?.length > 0) {
        setConfigs(data);
      }
    };
    
    const fetchUserData = async () => {
      const data = await selectQuery('user', {}, '*');
      if (data?.length > 0) {
        setUserData({
          name: data[0].name || '@appsinppuser',
          email: data[0].email || '<EMAIL>',
          phone: data[0].phone || '+91 8129999999'
        });
      }
    };
    
    fetchConfigs();
    fetchUserData();
  }, []);

  const handleEdit = (config) => {
    try {
      setSelectedConfig({
        ...config,
        mainHeader: typeof config.mainHeader === 'string' ? JSON.parse(config.mainHeader) : config.mainHeader,
        showDataHeader: typeof config.showDataHeader === 'string' ? JSON.parse(config.showDataHeader) : config.showDataHeader,
      });
      setModalVisible(true);
    } catch (error) {
      console.error('Error parsing config data:', error);
      Alert.alert('Error', 'Failed to load configuration data');
    }
  };

  const handleToggle = (key) => {
    setSelectedConfig((prevConfig) => ({
      ...prevConfig,
      [key]: prevConfig[key] ? 0 : 1,
    }));
  };

  const handleHeaderChange = (value) => {
    setSelectedConfig((prevConfig) => ({
      ...prevConfig,
      mainHeader: [
        {
          headerKey: value,
          headerValue:
            prevConfig.showDataHeader.find((h) => h.headerKey === value)?.headerValue || "",
        },
      ],
    }));
  };

  const handleUpdate = async () => {
    if (selectedConfig) {
      try {
        const updatedConfig = {
          ...selectedConfig,
          mainHeader: JSON.stringify(selectedConfig.mainHeader),
          showDataHeader: JSON.stringify(selectedConfig.showDataHeader),
        };
        
        await updateQuery('config', updatedConfig, { uid: updatedConfig.uid });
        
        setConfigs((prevConfigs) =>
          prevConfigs.map((config) =>
            config.uid === updatedConfig.uid ? updatedConfig : config
          )
        );
        
        setModalVisible(false);
        Alert.alert('Success', 'Configuration updated successfully!');
      } catch (error) {
        console.error('Update error:', error);
        Alert.alert('Error', 'Failed to update configuration');
      }
    }
  };

  const exportDatabase = async () => {
    try {
      // Get all tables except user and migrations
      const tablesQuery = "SELECT name FROM sqlite_master WHERE type='table' AND name NOT IN ('user', 'migrations', 'sqlite_sequence')";
      const tables = await executeQuery(tablesQuery);
      
      let exportData = {};
      
      // For each table, get all data
      for (const table of tables) {
        const tableName = table.name;
        const tableData = await selectQuery(tableName, {}, '*');
        exportData[tableName] = tableData;
      }
      
      // Convert to JSON and encrypt
      const jsonData = JSON.stringify(exportData);
      const encryptedData = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        jsonData
      );
      
      // Create a secure file with custom extension
      const timestamp = new Date().getTime();
      const secureFilePath = `${FileSystem.documentDirectory}lakshcrypt_backup_${timestamp}.lcrypt`;
      
      // Create a header with app signature to verify file authenticity
      const fileHeader = "LAKSHCRYPT_SECURE_BACKUP_V1";
      const fileContent = fileHeader + "|" + encryptedData + "|" + jsonData;
      
      await FileSystem.writeAsStringAsync(secureFilePath, fileContent);
      
      return secureFilePath;
    } catch (error) {
      console.error('Export error:', error);
      throw error;
    }
  };

  const importDatabase = async (fileUri) => {
    try {
      const fileContent = await FileSystem.readAsStringAsync(fileUri);
      
      // Verify file header
      if (!fileContent.startsWith("LAKSHCRYPT_SECURE_BACKUP_V1|")) {
        throw new Error("Invalid backup file format");
      }
      
      const parts = fileContent.split("|");
      if (parts.length !== 3) {
        throw new Error("Corrupted backup file");
      }
      
      const [, checksum, jsonData] = parts;
      
      // Verify checksum
      const calculatedChecksum = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        jsonData
      );
      
      if (checksum !== calculatedChecksum) {
        throw new Error("Backup file integrity check failed");
      }
      
      // Parse the data
      const importData = JSON.parse(jsonData);
      
      // Import each table
      for (const [tableName, tableData] of Object.entries(importData)) {
        // First clear the table
        await executeQuery(`DELETE FROM ${tableName}`);
        
        // Then insert all records
        for (const record of tableData) {
          const { uid, ...recordWithoutUid } = record;
          await updateQuery(tableName, recordWithoutUid, { uid });
        }
      }
      
      return true;
    } catch (error) {
      console.error('Import error:', error);
      throw error;
    }
  };

  const handleExport = async () => {
    try {
      setIsExporting(true);
      setProgress(0.1);
      
      const secureFilePath = await exportDatabase();
      
      setProgress(1);
      setIsExporting(false);
      
      Alert.alert(
        'Export Successful',
        `Database exported securely to:\n${secureFilePath}\n\nThis file can only be imported back into LAKSHCRYPT.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      setIsExporting(false);
      Alert.alert('Export Failed', error.message);
    }
  };

  const handleImport = async () => {
    try {
      setIsImporting(true);
      setProgress(0.1);
      
      const result = await DocumentPicker.getDocumentAsync();
      
      if (result.canceled) {
        setIsImporting(false);
        return;
      }
      
      const fileUri = result.assets[0].uri;
      const fileName = result.assets[0].name;
      
      if (!fileName.endsWith('.lcrypt')) {
        setIsImporting(false);
        Alert.alert('Invalid File', 'Please select a valid LAKSHCRYPT backup file (.lcrypt)');
        return;
      }
      
      setProgress(0.3);
      
      await importDatabase(fileUri);
      
      setProgress(1);
      setIsImporting(false);
      
      Alert.alert(
        'Import Successful',
        'Database imported successfully. The app will now reload.',
        [{ 
          text: 'OK',
          onPress: () => {
            // Reload app or navigate to refresh data
            navigation.reset({
              index: 0,
              routes: [{ name: 'Dashboard' }],
            });
          }
        }]
      );
    } catch (error) {
      setIsImporting(false);
      Alert.alert('Import Failed', error.message);
    }
  };

  const handleSubmitContactForm = () => {
    // Here you would typically send this data to your backend
    Alert.alert(
      'Message Sent',
      'Thank you for your message. We will get back to you soon!'
    );
    
    // Reset form
    setContactForm({
      name: '',
      email: '',
      phone: '',
      message: ''
    });
  };

  const toggleConfigExpand = (configId) => {
    setExpandedConfigId(expandedConfigId === configId ? null : configId);
  };

  // Create sections for FlatList
  const sections = [
    { type: 'profile', data: userData },
    { type: 'actions', data: null },
    ...(isExporting || isImporting ? [{ type: 'progress', data: { progress, isExporting, isImporting } }] : []),
    { type: 'configs', data: configs },
    { type: 'contact', data: contactForm }
  ];

  const renderSection = ({ item }) => {
    switch (item.type) {
      case 'profile':
        return (
          <View style={styles.headerContainer}>
            <LinearGradient
              colors={['#4285F4', '#6366F1']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.headerGradient}
            >

              {/* Profile Card */}
              <View style={styles.profileCard}>
                <View style={styles.profileHeader}>
                  <View style={styles.avatarContainer}>
                    <Avatar.Text
                      size={80}
                      label={item.data.name.substring(1, 3).toUpperCase()}
                      backgroundColor="rgba(255, 255, 255, 0.2)"
                      color="#fff"
                      labelStyle={styles.avatarLabel}
                    />
                  </View>
                  <View style={styles.profileInfo}>
                    <Text style={styles.profileName}>{item.data.name}</Text>
                    <Text style={styles.profileRole}>{item.data.email}</Text>
                    <Text style={styles.profilePhone}>{item.data.phone}</Text>
                  </View>
                </View>

                {/* Action Buttons */}
                <View style={styles.actionButtonsRow}>
                  <TouchableOpacity style={styles.messageButton} onPress={handleImport} disabled={isImporting}>
                    <Text style={styles.messageButtonText}>
                      {isImporting ? 'Importing...' : 'Import Data'}
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.followButton} onPress={handleExport} disabled={isExporting}>
                    <MaterialIcons name="file-download" size={18} color="white" style={styles.followIcon} />
                    <Text style={styles.followButtonText}>
                      {isExporting ? 'Exporting...' : 'Export Data'}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </LinearGradient>
          </View>
        );

      case 'actions':
        return (
          <View style={styles.statusCard}>
            <View style={styles.statusHeader}>
              <MaterialIcons name="settings" size={24} color="#4285F4" />
              <Text style={styles.statusTitle}>APP CONFIGURATIONS</Text>
            </View>
            <Text style={styles.statusText}>
              Manage your app settings and configurations. You have {configs.length} configurations available for customization.
            </Text>
          </View>
        );

      case 'progress':
        return (
          <View style={styles.progressContainer}>
            <ProgressBar progress={item.data.progress} color="#4A67F0" style={styles.progressBar} />
            <Text style={styles.progressText}>
              {item.data.isExporting ? 'Exporting database...' : 'Importing database...'}
              {Math.round(item.data.progress * 100)}%
            </Text>
          </View>
        );

      case 'configs':
        return (
          <View style={styles.configsContainer}>
            {item.data.map((config, index) => (
              <TouchableOpacity
                key={config.uid}
                style={styles.configCard}
                onPress={() => handleEdit(config)}
              >
                <View style={styles.configHeader}>
                  <View style={styles.configIconContainer}>
                    <MaterialIcons name="settings" size={24} color="#4285F4" />
                  </View>
                  <View style={styles.configInfo}>
                    <Text style={styles.configTitle}>{config.title}</Text>
                    <Text style={styles.configSubtitle}>
                      {config.isVisible ? 'Visible' : 'Hidden'} • {config.isShare ? 'Shareable' : 'Private'}
                    </Text>
                  </View>
                  <MaterialIcons name="chevron-right" size={20} color="#4285F4" />
                </View>
              </TouchableOpacity>
            ))}
          </View>
        );

      case 'contact':
        return (
          <TouchableOpacity
            style={styles.projectCard}
            onPress={handleSubmitContactForm}
          >
            <Text style={styles.projectText}>QUESTIONS / SUGGESTIONS</Text>
            <MaterialIcons name="arrow-forward" size={24} color="#4285F4" />
          </TouchableOpacity>
        );

      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={sections}
        keyExtractor={(item, index) => `${item.type}-${index}`}
        renderItem={renderSection}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.flatListContent}
        style={styles.flatList}
      />

      {/* Configuration Edit Modal */}
      <Modal visible={modalVisible} animationType="slide" transparent={true}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {selectedConfig?.title} Settings
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <MaterialIcons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <View style={styles.formContainer}>
              <View style={styles.checkboxRow}>
                <Text style={styles.checkboxLabel}>Share</Text>
                <TouchableOpacity
                  style={[styles.checkbox, selectedConfig?.isShare && styles.checkboxChecked]}
                  onPress={() => handleToggle("isShare")}
                >
                  {selectedConfig?.isShare && (
                    <MaterialIcons name="check" size={16} color="white" />
                  )}
                </TouchableOpacity>
              </View>

              <View style={styles.checkboxRow}>
                <Text style={styles.checkboxLabel}>Visible</Text>
                <TouchableOpacity
                  style={[styles.checkbox, selectedConfig?.isVisible && styles.checkboxChecked]}
                  onPress={() => handleToggle("isVisible")}
                >
                  {selectedConfig?.isVisible && (
                    <MaterialIcons name="check" size={16} color="white" />
                  )}
                </TouchableOpacity>
              </View>

              <View style={styles.divider} />

              <Text style={styles.pickerLabel}>Main Header:</Text>
              <View style={styles.pickerContainer}>
                <Text style={styles.pickerValue}>
                  {selectedConfig?.mainHeader?.[0]?.headerKey || 'Select header'}
                </Text>
              </View>

              <View style={styles.divider} />

              <Text style={styles.subTitle}>Show Data Headers</Text>
              {selectedConfig?.showDataHeader?.map((header) => (
                <View key={header.headerKey} style={styles.checkboxRow}>
                  <Text style={styles.checkboxLabel}>{header.headerValue}</Text>
                  <TouchableOpacity
                    style={[styles.checkbox, header.isVisible && styles.checkboxChecked]}
                    onPress={() => {
                      setSelectedConfig((prevConfig) => ({
                        ...prevConfig,
                        showDataHeader: prevConfig.showDataHeader.map((h) =>
                          h.headerKey === header.headerKey
                            ? { ...h, isVisible: h.isVisible ? 0 : 1 }
                            : h
                        ),
                      }));
                    }}
                  >
                    {header.isVisible && (
                      <MaterialIcons name="check" size={16} color="white" />
                    )}
                  </TouchableOpacity>
                </View>
              ))}

              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setModalVisible(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.updateButton}
                  onPress={handleUpdate}
                >
                  <Text style={styles.updateButtonText}>Update</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F5F5F5"
  },
  flatList: {
    flex: 1,
  },
  flatListContent: {
    paddingBottom: 20,
  },

  // Header Styles
  headerContainer: {
    marginBottom: 0,
  },
  headerGradient: {
    paddingTop: 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 30,
  },
  backButton: {
    padding: 8,
  },
  menuButton: {
    padding: 8,
  },

  // Profile Card Styles
  profileCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    marginHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  avatarContainer: {
    marginRight: 16,
  },
  avatarLabel: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  profileRole: {
    fontSize: 14,
    color: '#4285F4',
    fontWeight: '500',
  },
  profilePhone: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '400',
    marginTop: 2,
  },

  // Stats Row
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
    paddingVertical: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#999',
    fontWeight: '600',
    letterSpacing: 1,
  },

  // Action Buttons
  actionButtonsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  messageButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#4285F4',
    alignItems: 'center',
  },
  messageButtonText: {
    color: '#4285F4',
    fontWeight: '600',
    fontSize: 16,
  },
  followButton: {
    flex: 1,
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    backgroundColor: '#4285F4',
    alignItems: 'center',
    justifyContent: 'center',
  },
  followIcon: {
    marginRight: 8,
  },
  followButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },

  // Status Card
  statusCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 20,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#999',
    marginLeft: 8,
    letterSpacing: 1,
  },
  statusText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },

  // Stats Card
  statsCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 20,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statBox: {
    alignItems: 'center',
    flex: 1,
  },

  // Project Card
  projectCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 20,
    marginTop: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  projectText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#999',
    letterSpacing: 1,
  },

  // Config Cards
  configsContainer: {
    marginHorizontal: 20,
    marginTop: 20,
  },
  configCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  configHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  configIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F4FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  configInfo: {
    flex: 1,
  },
  configTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  configSubtitle: {
    fontSize: 12,
    color: '#999',
  },
  editIconButton: {
    padding: 8,
  },

  // Modal Styles
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
    padding: 16,
  },
  modalContent: {
    borderRadius: 20,
    backgroundColor: "#fff",
    maxHeight: "90%",
    marginHorizontal: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  formContainer: {
    padding: 20,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  messageInputWrapper: {
    alignItems: 'flex-start',
    minHeight: 80,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  messageInput: {
    textAlignVertical: 'top',
    minHeight: 60,
  },
  submitButton: {
    flexDirection: 'row',
    backgroundColor: '#4285F4',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
  },
  submitIcon: {
    marginRight: 8,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  checkboxRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    marginBottom: 8,
  },
  checkboxLabel: {
    fontSize: 16,
    color: "#333",
    fontWeight: '500',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#4285F4',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#4285F4',
  },
  divider: {
    marginVertical: 16,
    height: 1,
    backgroundColor: "#E9ECEF",
  },
  pickerLabel: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
    color: "#333",
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: "#E9ECEF",
    borderRadius: 12,
    marginBottom: 12,
    backgroundColor: "#F8F9FA",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  pickerValue: {
    fontSize: 16,
    color: "#333",
  },
  subTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
    color: "#333",
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: "space-between",
    marginTop: 24,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E9ECEF',
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
  updateButton: {
    flex: 1,
    backgroundColor: "#4285F4",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  updateButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SettingsScreen;