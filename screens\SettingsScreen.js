import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Modal,
  TouchableOpacity,
  Image,
  TextInput,
  Alert
} from "react-native";
import { Checkbox, Button, Card, Divider, IconButton, ProgressBar, Avatar } from "react-native-paper";
import { Picker } from "@react-native-picker/picker";
import { LinearGradient } from 'expo-linear-gradient';
import { selectQuery, updateQuery, executeQuery } from "../src/controller";
import * as FileSystem from 'expo-file-system';
import * as DocumentPicker from 'expo-document-picker';
import * as Crypto from 'expo-crypto';
import { MaterialIcons, FontAwesome5 } from '@expo/vector-icons';

const subjectColors = ['#2ecc71', '#3498db', '#9b59b6', '#f1c40f', '#e74c3c', '#34495e'];

const SettingsScreen = ({ navigation }) => {
  const [configs, setConfigs] = useState([]);
  const [selectedConfig, setSelectedConfig] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [expandedConfigId, setExpandedConfigId] = useState(null);
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    phone: '',
    message: ''
  });
  const [userData, setUserData] = useState({
    name: '@appsinppuser',
    email: '<EMAIL>',
    phone: '+91 8129999999'
  });

  useEffect(() => {
    const fetchConfigs = async () => {
      const data = await selectQuery('config', {}, '*', { orderBy: 'title' });
      if (data?.length > 0) {
        setConfigs(data);
      }
    };
    
    const fetchUserData = async () => {
      const data = await selectQuery('user', {}, '*');
      if (data?.length > 0) {
        setUserData({
          name: data[0].name || '@appsinppuser',
          email: data[0].email || '<EMAIL>',
          phone: data[0].phone || '+91 8129999999'
        });
      }
    };
    
    fetchConfigs();
    fetchUserData();
  }, []);

  const handleEdit = (config) => {
    setSelectedConfig({
      ...config,
      mainHeader: JSON.parse(config.mainHeader),
      showDataHeader: JSON.parse(config.showDataHeader),
    });
    setModalVisible(true);
  };

  const handleToggle = (key) => {
    setSelectedConfig((prevConfig) => ({
      ...prevConfig,
      [key]: prevConfig[key] ? 0 : 1,
    }));
  };

  const handleHeaderChange = (value) => {
    setSelectedConfig((prevConfig) => ({
      ...prevConfig,
      mainHeader: [
        {
          headerKey: value,
          headerValue:
            prevConfig.showDataHeader.find((h) => h.headerKey === value)?.headerValue || "",
        },
      ],
    }));
  };

  const handleUpdate = async () => {
    if (selectedConfig) {
      try {
        const updatedConfig = {
          ...selectedConfig,
          mainHeader: JSON.stringify(selectedConfig.mainHeader),
          showDataHeader: JSON.stringify(selectedConfig.showDataHeader),
        };
        
        await updateQuery('config', updatedConfig, { uid: updatedConfig.uid });
        
        setConfigs((prevConfigs) =>
          prevConfigs.map((config) =>
            config.uid === updatedConfig.uid ? updatedConfig : config
          )
        );
        
        setModalVisible(false);
        Alert.alert('Success', 'Configuration updated successfully!');
      } catch (error) {
        console.error('Update error:', error);
        Alert.alert('Error', 'Failed to update configuration');
      }
    }
  };

  const exportDatabase = async () => {
    try {
      // Get all tables except user and migrations
      const tablesQuery = "SELECT name FROM sqlite_master WHERE type='table' AND name NOT IN ('user', 'migrations', 'sqlite_sequence')";
      const tables = await executeQuery(tablesQuery);
      
      let exportData = {};
      
      // For each table, get all data
      for (const table of tables) {
        const tableName = table.name;
        const tableData = await selectQuery(tableName, {}, '*');
        exportData[tableName] = tableData;
      }
      
      // Convert to JSON and encrypt
      const jsonData = JSON.stringify(exportData);
      const encryptedData = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        jsonData
      );
      
      // Create a secure file with custom extension
      const timestamp = new Date().getTime();
      const secureFilePath = `${FileSystem.documentDirectory}lakshcrypt_backup_${timestamp}.lcrypt`;
      
      // Create a header with app signature to verify file authenticity
      const fileHeader = "LAKSHCRYPT_SECURE_BACKUP_V1";
      const fileContent = fileHeader + "|" + encryptedData + "|" + jsonData;
      
      await FileSystem.writeAsStringAsync(secureFilePath, fileContent);
      
      return secureFilePath;
    } catch (error) {
      console.error('Export error:', error);
      throw error;
    }
  };

  const importDatabase = async (fileUri) => {
    try {
      const fileContent = await FileSystem.readAsStringAsync(fileUri);
      
      // Verify file header
      if (!fileContent.startsWith("LAKSHCRYPT_SECURE_BACKUP_V1|")) {
        throw new Error("Invalid backup file format");
      }
      
      const parts = fileContent.split("|");
      if (parts.length !== 3) {
        throw new Error("Corrupted backup file");
      }
      
      const [, checksum, jsonData] = parts;
      
      // Verify checksum
      const calculatedChecksum = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        jsonData
      );
      
      if (checksum !== calculatedChecksum) {
        throw new Error("Backup file integrity check failed");
      }
      
      // Parse the data
      const importData = JSON.parse(jsonData);
      
      // Import each table
      for (const [tableName, tableData] of Object.entries(importData)) {
        // First clear the table
        await executeQuery(`DELETE FROM ${tableName}`);
        
        // Then insert all records
        for (const record of tableData) {
          const { uid, ...recordWithoutUid } = record;
          await updateQuery(tableName, recordWithoutUid, { uid });
        }
      }
      
      return true;
    } catch (error) {
      console.error('Import error:', error);
      throw error;
    }
  };

  const handleExport = async () => {
    try {
      setIsExporting(true);
      setProgress(0.1);
      
      const secureFilePath = await exportDatabase();
      
      setProgress(1);
      setIsExporting(false);
      
      Alert.alert(
        'Export Successful',
        `Database exported securely to:\n${secureFilePath}\n\nThis file can only be imported back into LAKSHCRYPT.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      setIsExporting(false);
      Alert.alert('Export Failed', error.message);
    }
  };

  const handleImport = async () => {
    try {
      setIsImporting(true);
      setProgress(0.1);
      
      const result = await DocumentPicker.getDocumentAsync();
      
      if (result.canceled) {
        setIsImporting(false);
        return;
      }
      
      const fileUri = result.assets[0].uri;
      const fileName = result.assets[0].name;
      
      if (!fileName.endsWith('.lcrypt')) {
        setIsImporting(false);
        Alert.alert('Invalid File', 'Please select a valid LAKSHCRYPT backup file (.lcrypt)');
        return;
      }
      
      setProgress(0.3);
      
      await importDatabase(fileUri);
      
      setProgress(1);
      setIsImporting(false);
      
      Alert.alert(
        'Import Successful',
        'Database imported successfully. The app will now reload.',
        [{ 
          text: 'OK',
          onPress: () => {
            // Reload app or navigate to refresh data
            navigation.reset({
              index: 0,
              routes: [{ name: 'Dashboard' }],
            });
          }
        }]
      );
    } catch (error) {
      setIsImporting(false);
      Alert.alert('Import Failed', error.message);
    }
  };

  const handleSubmitContactForm = () => {
    // Here you would typically send this data to your backend
    Alert.alert(
      'Message Sent',
      'Thank you for your message. We will get back to you soon!'
    );
    
    // Reset form
    setContactForm({
      name: '',
      email: '',
      phone: '',
      message: ''
    });
  };

  const toggleConfigExpand = (configId) => {
    setExpandedConfigId(expandedConfigId === configId ? null : configId);
  };

  // Create sections for FlatList
  const sections = [
    { type: 'profile', data: userData },
    { type: 'actions', data: null },
    ...(isExporting || isImporting ? [{ type: 'progress', data: { progress, isExporting, isImporting } }] : []),
    { type: 'configs', data: configs },
    { type: 'contact', data: contactForm }
  ];

  const renderSection = ({ item }) => {
    switch (item.type) {
      case 'profile':
        return (
          <View style={styles.profileCard}>
            <LinearGradient
              colors={['#FF6B6B', '#4ECDC4', '#45B7D1']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.profileGradient}
            >
              <View style={styles.profileContent}>
                <View style={styles.avatarContainer}>
                  <Avatar.Text
                    size={80}
                    label={item.data.name.substring(0, 2).toUpperCase()}
                    backgroundColor="rgba(255, 255, 255, 0.2)"
                    color="#fff"
                    labelStyle={styles.avatarLabel}
                  />
                </View>
                <View style={styles.userInfo}>
                  <Text style={styles.username}>{item.data.name}</Text>
                  <View style={styles.userDetailRow}>
                    <MaterialIcons name="email" size={18} color="rgba(255, 255, 255, 0.8)" />
                    <Text style={styles.userDetail}>{item.data.email}</Text>
                  </View>
                  <View style={styles.userDetailRow}>
                    <MaterialIcons name="phone" size={18} color="rgba(255, 255, 255, 0.8)" />
                    <Text style={styles.userDetail}>{item.data.phone}</Text>
                  </View>
                </View>
              </View>
            </LinearGradient>
          </View>
        );

      case 'actions':
        return (
          <View style={styles.actionButtonsContainer}>
            <TouchableOpacity onPress={handleImport} disabled={isImporting}>
              <LinearGradient
                colors={['#667eea', '#764ba2']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={[styles.actionButton, isImporting && styles.disabledButton]}
              >
                <FontAwesome5 name="file-import" size={20} color="#fff" />
                <Text style={styles.actionButtonText}>
                  {isImporting ? '⏳ Importing...' : '📥 Import Data'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity onPress={handleExport} disabled={isExporting}>
              <LinearGradient
                colors={['#FF9A8B', '#A8E6CF']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={[styles.actionButton, isExporting && styles.disabledButton]}
              >
                <FontAwesome5 name="file-export" size={20} color="#fff" />
                <Text style={styles.actionButtonText}>
                  {isExporting ? '⏳ Exporting...' : '📤 Export Data'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        );

      case 'progress':
        return (
          <View style={styles.progressContainer}>
            <ProgressBar progress={item.data.progress} color="#4A67F0" style={styles.progressBar} />
            <Text style={styles.progressText}>
              {item.data.isExporting ? 'Exporting database...' : 'Importing database...'}
              {Math.round(item.data.progress * 100)}%
            </Text>
          </View>
        );

      case 'configs':
        return (
          <Card style={styles.sectionCard}>
            <Card.Title
              title="App Configurations"
              titleStyle={styles.sectionTitle}
              left={(props) => <MaterialIcons name="settings" size={24} color="#4A67F0" {...props} />}
            />
            <Card.Content>
              {item.data.map((config, index) => (
                <TouchableOpacity key={config.uid} onPress={() => toggleConfigExpand(config.uid)}>
                  <Card style={styles.configCard}>
                    <View style={[styles.configCardHeader, { backgroundColor: subjectColors[index % subjectColors.length] }]}>
                      <Text style={styles.configCardTitle}>{config.title}</Text>
                      <IconButton
                        icon={expandedConfigId === config.uid ? "chevron-up" : "chevron-down"}
                        size={24}
                        color="#fff"
                        onPress={() => toggleConfigExpand(config.uid)}
                      />
                    </View>

                    {expandedConfigId === config.uid && (
                      <Card.Content style={styles.configDetails}>
                        <Text style={styles.configDetailText}>
                          <Text style={styles.configDetailLabel}>Table Key: </Text>
                          {config.table_key}
                        </Text>
                        <Text style={styles.configDetailText}>
                          <Text style={styles.configDetailLabel}>Shareable: </Text>
                          {config.isShare ? 'Yes' : 'No'}
                        </Text>
                        <Text style={styles.configDetailText}>
                          <Text style={styles.configDetailLabel}>Visible: </Text>
                          {config.isVisible ? 'Yes' : 'No'}
                        </Text>

                        <Button
                          mode="contained"
                          style={styles.editButton}
                          onPress={() => handleEdit(config)}
                        >
                          Edit Configuration
                        </Button>
                      </Card.Content>
                    )}
                  </Card>
                </TouchableOpacity>
              ))}
            </Card.Content>
          </Card>
        );

      case 'contact':
        return (
          <Card style={styles.sectionCard}>
            <Card.Title
              title="Ask Me"
              titleStyle={styles.sectionTitle}
              left={(props) => <MaterialIcons name="contact-support" size={24} color="#4A67F0" {...props} />}
            />
            <Card.Content>
              <TextInput
                style={styles.input}
                placeholder="Your Name"
                value={item.data.name}
                onChangeText={(text) => setContactForm({...contactForm, name: text})}
              />
              <TextInput
                style={styles.input}
                placeholder="Your Email"
                keyboardType="email-address"
                value={item.data.email}
                onChangeText={(text) => setContactForm({...contactForm, email: text})}
              />
              <TextInput
                style={styles.input}
                placeholder="Your Phone"
                keyboardType="phone-pad"
                value={item.data.phone}
                onChangeText={(text) => setContactForm({...contactForm, phone: text})}
              />
              <TextInput
                style={[styles.input, styles.messageInput]}
                placeholder="Your Message"
                multiline
                numberOfLines={4}
                value={item.data.message}
                onChangeText={(text) => setContactForm({...contactForm, message: text})}
              />
              <TouchableOpacity onPress={handleSubmitContactForm}>
                <LinearGradient
                  colors={['#4ECDC4', '#44A08D']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.submitButton}
                >
                  <Text style={styles.submitButtonText}>✨ Submit Message</Text>
                </LinearGradient>
              </TouchableOpacity>
            </Card.Content>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={sections}
        keyExtractor={(item, index) => `${item.type}-${index}`}
        renderItem={renderSection}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.flatListContent}
      />

      {/* Configuration Edit Modal */}
      <Modal visible={modalVisible} animationType="slide" transparent={true}>
        <View style={styles.modalContainer}>
          <Card style={styles.modalContent}>
            <Card.Title 
              title={`${selectedConfig?.title} Settings`} 
              titleStyle={{fontWeight: 'bold'}}
              right={(props) => (
                <IconButton 
                  {...props} 
                  icon="close" 
                  onPress={() => setModalVisible(false)} 
                />
              )}
            />
            <Card.Content>
              <View style={styles.checkboxRow}>
                <Text style={styles.checkboxLabel}>Share</Text>
                <Checkbox
                  status={selectedConfig?.isShare ? "checked" : "unchecked"}
                  onPress={() => handleToggle("isShare")}
                  color="#4A67F0"
                />
              </View>
              <View style={styles.checkboxRow}>
                <Text style={styles.checkboxLabel}>Visible</Text>
                <Checkbox
                  status={selectedConfig?.isVisible ? "checked" : "unchecked"}
                  onPress={() => handleToggle("isVisible")}
                  color="#4A67F0"
                />
              </View>
              <Divider style={styles.divider} />
              <Text style={styles.pickerLabel}>Main Header:</Text>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={selectedConfig?.mainHeader[0]?.headerKey}
                  onValueChange={(value) => handleHeaderChange(value)}
                  style={styles.picker}>
                  {selectedConfig?.showDataHeader.map((header, index) => (
                    <Picker.Item key={index} label={header.headerValue} value={header.headerKey} />
                  ))}
                </Picker>
              </View>
              <Divider style={styles.divider} />
              <Text style={styles.subTitle}>Show Data Headers</Text>
              <FlatList
                data={selectedConfig?.showDataHeader}
                keyExtractor={(item) => item.headerKey}
                renderItem={({ item }) => (
                  <View style={styles.checkboxRow}>
                    <Text style={styles.checkboxLabel}>{item.headerValue}</Text>
                    <Checkbox
                      status={item.isVisible ? "checked" : "unchecked"}
                      onPress={() => {
                        setSelectedConfig((prevConfig) => ({
                          ...prevConfig,
                          showDataHeader: prevConfig.showDataHeader.map((h) =>
                            h.headerKey === item.headerKey
                              ? { ...h, isVisible: h.isVisible ? 0 : 1 }
                              : h
                          ),
                        }));
                      }}
                      color="#4A67F0"
                    />
                  </View>
                )}
              />
            </Card.Content>
            <Card.Actions style={styles.modalActions}>
              <Button onPress={() => setModalVisible(false)}>Cancel</Button>
              <Button mode="contained" onPress={handleUpdate} style={styles.updateButton}>
                Update
              </Button>
            </Card.Actions>
          </Card>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F7F9FC"
  },
  profileCard: {
    margin: 16,
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 12,
  },
  profileGradient: {
    padding: 24,
  },
  profileContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatarContainer: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  avatarLabel: {
    fontSize: 28,
    fontWeight: '800',
  },
  userInfo: {
    marginLeft: 20,
    flex: 1,
  },
  username: {
    fontSize: 24,
    fontWeight: "800",
    marginBottom: 8,
    color: "white",
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  userDetailRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 6,
  },
  userDetail: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.9)",
    marginLeft: 8,
    fontWeight: "600",
    letterSpacing: 0.3,
  },
  actionButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginHorizontal: 16,
    marginBottom: 20,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 16,
    flex: 0.48,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  disabledButton: {
    opacity: 0.6,
  },
  actionButtonText: {
    color: "#fff",
    fontWeight: "700",
    marginLeft: 10,
    fontSize: 16,
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  progressContainer: {
    marginHorizontal: 16,
    marginBottom: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  progressBar: {
    height: 10,
    borderRadius: 8,
    backgroundColor: 'rgba(103, 102, 234, 0.2)',
  },
  progressText: {
    textAlign: "center",
    marginTop: 12,
    color: "#475569",
    fontSize: 14,
    fontWeight: "600",
    letterSpacing: 0.3,
  },
  sectionCard: {
    margin: 16,
    marginTop: 0,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 20,
    elevation: 12,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  sectionTitle: {
    fontWeight: "700",
    fontSize: 20,
    color: "#1e293b",
    letterSpacing: 0.5,
  },
  configCard: {
    marginBottom: 12,
    borderRadius: 8,
    overflow: "hidden",
    elevation: 2,
  },
  configCardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  configCardTitle: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
  },
  configDetails: {
    padding: 12,
    backgroundColor: "#f9f9f9",
  },
  configDetailText: {
    marginBottom: 8,
    fontSize: 14,
    color: "#333",
  },
  configDetailLabel: {
    fontWeight: "bold",
    color: "#555",
  },
  editButton: {
    marginTop: 8,
    backgroundColor: "#4A67F0",
  },
  input: {
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 2,
    borderColor: "rgba(78, 205, 196, 0.3)",
    fontSize: 16,
    shadowColor: '#4ECDC4',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  messageInput: {
    height: 120,
    textAlignVertical: "top",
  },
  submitButton: {
    marginTop: 16,
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#4ECDC4',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
    padding: 16,
  },
  modalContent: {
    borderRadius: 12,
    padding: 8,
    backgroundColor: "#fff",
    maxHeight: "80%",
  },
  checkboxRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
  },
  checkboxLabel: {
    fontSize: 16,
    color: "#333",
  },
  divider: {
    marginVertical: 12,
    height: 1,
    backgroundColor: "#e0e0e0",
  },
  pickerLabel: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 8,
    color: "#333",
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    marginBottom: 12,
    backgroundColor: "#f5f5f5",
  },
  picker: {
    height: 50,
  },
  subTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 8,
    color: "#333",
  },
  modalActions: {
    justifyContent: "flex-end",
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  updateButton: {
    backgroundColor: "#4A67F0",
  },
  flatListContent: {
    paddingBottom: 20,
  }
});

export default SettingsScreen;