import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import MIcon from 'react-native-vector-icons/MaterialIcons';
import MCIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';

const BottomTabNavigator = ({menu}) => {
  const [activeTab, setActiveTab] = useState(menu);
  const navigation = useNavigation();

  const handlePress = (screen) => {
    setActiveTab(screen);
    navigation.navigate(screen);
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['rgba(255, 107, 107, 0.95)', 'rgba(78, 205, 196, 0.95)', 'rgba(69, 183, 209, 0.95)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.tabBackground}
      >
        {/* Left Side Tabs */}
        {[
          { name: 'Finance', icon: <MCIcon name="bank-outline" size={28} /> },
          { name: 'Digital', icon: <MIcon name="device-hub" size={28} /> },
        ].map((item) => (
          <TouchableOpacity
            key={item.name}
            style={[styles.tabButton, activeTab === item.name && styles.activeTab]}
            onPress={() => handlePress(item.name)}
          >
            {activeTab === item.name && (
              <LinearGradient
                colors={['#ffffff', '#f8fafc']}
                style={styles.activeIndicator}
              >
                <View style={styles.activeDot} />
              </LinearGradient>
            )}
            {React.cloneElement(item.icon, {
              color: activeTab === item.name ? '#2C3E50' : 'rgba(255, 255, 255, 0.9)'
            })}
          </TouchableOpacity>
        ))}

        {/* Center Home Button */}
        <View style={styles.micContainer}>
          <LinearGradient
            colors={['#ffffff', '#f8fafc']}
            style={styles.micButton}
          >
            <TouchableOpacity style={styles.homeButton} onPress={() => handlePress('Dashboard')}>
              <MCIcon name="shield-home" size={40} color={'#FF6B6B'} />
            </TouchableOpacity>
          </LinearGradient>
        </View>

        {/* Right Side Tabs */}
        {[
          { name: 'Other', icon: <MIcon name="extension" size={28} /> },
          { name: 'Profile', icon: <Icon name="user" size={28} /> },
        ].map((item) => (
          <TouchableOpacity
            key={item.name}
            style={[styles.tabButton, activeTab === item.name && styles.activeTab]}
            onPress={() => handlePress(item.name)}
          >
            {activeTab === item.name && (
              <LinearGradient
                colors={['#ffffff', '#f8fafc']}
                style={styles.activeIndicator}
              >
                <View style={styles.activeDot} />
              </LinearGradient>
            )}
            {React.cloneElement(item.icon, {
              color: activeTab === item.name ? '#2C3E50' : 'rgba(255, 255, 255, 0.9)'
            })}
          </TouchableOpacity>
        ))}
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    width: '94%',
    height: 90,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: '3%',
    marginBottom: '5%'
  },
  tabBackground: {
    flexDirection: 'row',
    width: '100%',
    height: 75,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    shadowColor: '#667eea',
    shadowOpacity: 0.4,
    shadowRadius: 20,
    elevation: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    paddingVertical: 12,
  },
  activeIndicator: {
    position: 'absolute',
    top: -35,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#667eea',
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  activeDot: {
    width: 12,
    height: 12,
    backgroundColor: '#FF6B6B',
    borderRadius: 6,
  },
  micContainer: {
    position: 'relative',
    bottom: 25,
    width: 85,
    height: 85,
    borderRadius: 42.5,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#667eea',
    shadowOpacity: 0.4,
    shadowRadius: 20,
    elevation: 15,
  },
  micButton: {
    width: 75,
    height: 75,
    borderRadius: 37.5,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#667eea',
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 10,
  },
  homeButton: {
    width: '100%',
    height: '100%',
    borderRadius: 37.5,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default BottomTabNavigator;