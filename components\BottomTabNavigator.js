import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import MIcon from 'react-native-vector-icons/MaterialIcons';
import MCIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';

const BottomTabNavigator = ({menu}) => {
  const [activeTab, setActiveTab] = useState(menu);
  const navigation = useNavigation();

  const handlePress = (screen) => {
    setActiveTab(screen);
    navigation.navigate(screen);
  };

  return (
    <View style={styles.container}>
      <View style={styles.tabBackground}>
        {/* Left Side Tabs */}
        {[
          { name: 'Finance', icon: <MCIcon name="bank-outline" size={28} /> },
          { name: 'Digital', icon: <MIcon name="device-hub" size={28} /> },
        ].map((item) => (
          <TouchableOpacity
            key={item.name}
            style={[styles.tabButton, activeTab === item.name && styles.activeTab]}
            onPress={() => handlePress(item.name)}
          >
            {activeTab === item.name && (
              <LinearGradient
                colors={['#ffffff', '#f8fafc']}
                style={styles.activeIndicator}
              >
                <View style={styles.activeDot} />
              </LinearGradient>
            )}
            {React.cloneElement(item.icon, {
              color: activeTab === item.name ? '#4285F4' : '#9E9E9E'
            })}
          </TouchableOpacity>
        ))}

        {/* Center Home Button */}
        <View style={styles.micContainer}>
          <LinearGradient
            colors={['#ffffff', '#f8fafc']}
            style={styles.micButton}
          >
            <TouchableOpacity style={styles.homeButton} onPress={() => handlePress('Dashboard')}>
              <MCIcon name="plus" size={32} color={'white'} />
            </TouchableOpacity>
          </LinearGradient>
        </View>

        {/* Right Side Tabs */}
        {[
          { name: 'Other', icon: <MIcon name="extension" size={28} /> },
          { name: 'Profile', icon: <Icon name="user" size={28} /> },
        ].map((item) => (
          <TouchableOpacity
            key={item.name}
            style={[styles.tabButton, activeTab === item.name && styles.activeTab]}
            onPress={() => handlePress(item.name)}
          >
            {activeTab === item.name && (
              <LinearGradient
                colors={['#ffffff', '#f8fafc']}
                style={styles.activeIndicator}
              >
                <View style={styles.activeDot} />
              </LinearGradient>
            )}
            {React.cloneElement(item.icon, {
              color: activeTab === item.name ? '#4285F4' : '#9E9E9E'
            })}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    width: '94%',
    height: 90,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: '3%',
    marginBottom: '5%'
  },
  tabBackground: {
    flexDirection: 'row',
    width: '100%',
    height: 70,
    backgroundColor: 'white',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 8,
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    paddingVertical: 12,
  },
  activeIndicator: {
    position: 'absolute',
    top: -35,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#667eea',
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  activeDot: {
    width: 12,
    height: 12,
    backgroundColor: '#FF6B6B',
    borderRadius: 6,
  },
  micContainer: {
    position: 'relative',
    bottom: 20,
    width: 70,
    height: 70,
    borderRadius: 35,
    alignItems: 'center',
    justifyContent: 'center',
  },
  micButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#4285F4',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#4285F4',
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  homeButton: {
    width: '100%',
    height: '100%',
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default BottomTabNavigator;