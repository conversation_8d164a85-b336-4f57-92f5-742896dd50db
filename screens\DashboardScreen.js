import React, { useState, useEffect, use } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Feather, FontAwesome, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ActivityIndicator } from 'react-native-paper';
import BottomTabNavigator from '../components/BottomTabNavigator';
import { selectQuery, monthlyExpense } from '../src/controller';

const DashboardScreen = ({ navigation }) => {
  const [bankAccounts, setBankAccounts] = useState([]);
  const [cardDetails, setCardDetails] = useState([]);
  const [netBanking, setNetBanking] = useState([]);
  const [task, setTask] = useState([]);
  const [monthExpense, setMonthExpense] = useState([]);
  const [expense, setExpense] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalExpense, setTotalExpense] = useState(0);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const banks = await selectQuery('bank_account');
      const cards = await selectQuery('card_details');
      const netBanks = await selectQuery('net_banking');
      const tasks = await selectQuery('task');
      let year = new Date().getFullYear();
      let month = new Date().getMonth() + 1;
      const expenses = await monthlyExpense(year, month);
      const totalExpense = expenses.reduce((acc, expense) => acc + parseFloat(expense.amount), 0);
      const totalIncome = expenses.reduce((acc, expense) => acc + parseFloat(expense.remainingbalance), 0);
      setBankAccounts(banks || []);
      setCardDetails(cards || []);
      setNetBanking(netBanks || []);
      setTask(tasks || []);
      setExpense(expenses || []);
      setMonthExpense(totalExpense);
      setTotalExpense(totalIncome);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator animating={true} size="large" color="#3498db" />
        <Text style={styles.loadingText}>Loading Dashboard...</Text>
      </View>
    );
  }

  return (
    <>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>

        <LinearGradient
          colors={['#667eea', '#764ba2']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.vaultContainer}
        >
          <View style={styles.vaultContent}>
            <Text style={styles.vaultText}>Vault Locked</Text>
            <Text style={styles.vaultSubtext}>Secure & Protected</Text>
          </View>
          <View style={styles.vaultIconContainer}>
            <MaterialIcons name="fingerprint" size={52} color="white" />
          </View>
        </LinearGradient>

        <Text style={styles.sectionTitle}>Quick Access</Text>

        <View style={styles.quickAccess}>
          <TouchableOpacity onPress={() => navigation.navigate('Bank Account')}>
            <LinearGradient
              colors={['#4facfe', '#00f2fe']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.quickItem}
            >
              <View style={styles.quickIconContainer}>
                <FontAwesome name="university" size={32} color="white" />
              </View>
              <Text style={styles.quickLabel}>Bank Accounts</Text>
              <Text style={styles.quickCount}>{bankAccounts.length}</Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity onPress={() => navigation.navigate('Expense')}>
            <LinearGradient
              colors={['#43e97b', '#38f9d7']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.quickItem}
            >
              <View style={styles.quickIconContainer}>
                <MaterialCommunityIcons name="account-cash" size={32} color="white" />
              </View>
              <Text style={styles.quickLabel}>Expenses</Text>
              <Text style={styles.quickCount}>{expense.length}</Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity onPress={() => navigation.navigate('Net Banking')}>
            <LinearGradient
              colors={['#fa709a', '#fee140']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.quickItem}
            >
              <View style={styles.quickIconContainer}>
                <Feather name="key" size={32} color="white" />
              </View>
              <Text style={styles.quickLabel}>Passwords</Text>
              <Text style={styles.quickCount}>{netBanking.length}</Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity onPress={() => navigation.navigate('Notes')}>
            <LinearGradient
              colors={['#a8edea', '#fed6e3']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.quickItem}
            >
              <View style={styles.quickIconContainer}>
                <MaterialCommunityIcons name="note-text-outline" size={32} color="white" />
              </View>
              <Text style={styles.quickLabel}>Notes</Text>
              <Text style={styles.quickCount}>{task.length}</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Financial Insights</Text>

        <LinearGradient
          colors={['#667eea', '#764ba2']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.insightContainer}
        >
          <View style={styles.insightBox}>
            <Text style={styles.insightValue}>₹ {monthExpense}</Text>
            <Text style={styles.insightLabel}>This Month</Text>
          </View>
          <View style={styles.insightDivider} />
          <View style={styles.insightBox}>
            <Text style={styles.insightValue}>₹ {totalExpense}</Text>
            <Text style={styles.insightLabel}>Total Balance</Text>
          </View>
        </LinearGradient>
      </ScrollView>


      <BottomTabNavigator />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 100,
  },
  banner: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 16,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  bannerText: {
    color: '#1e293b',
    fontWeight: '700',
    marginLeft: 12,
    fontSize: 16,
    letterSpacing: 0.5,
  },
  vaultContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 24,
    borderRadius: 24,
    marginVertical: 20,
    shadowColor: '#6366f1',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 12,
    overflow: 'hidden',
  },
  vaultContent: {
    flex: 1,
  },
  vaultText: {
    fontSize: 28,
    fontWeight: '800',
    color: 'white',
    letterSpacing: 1,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  vaultSubtext: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '600',
    marginTop: 4,
    letterSpacing: 0.5,
  },
  vaultIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    padding: 16,
    borderRadius: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '700',
    marginVertical: 16,
    color: '#1e293b',
    letterSpacing: 0.5,
  },
  quickAccess: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    flexWrap: 'wrap',
  },
  quickItem: {
    width: '47%',
    padding: 24,
    borderRadius: 20,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  quickIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    padding: 12,
    borderRadius: 16,
    marginBottom: 12,
  },
  quickLabel: {
    fontSize: 14,
    color: 'white',
    fontWeight: '700',
    textAlign: 'center',
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  quickCount: {
    fontSize: 18,
    color: 'white',
    fontWeight: '800',
    textAlign: 'center',
    marginTop: 4,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  insightContainer: {
    padding: 24,
    borderRadius: 24,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#6366f1',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 12,
    overflow: 'hidden',
  },
  insightBox: {
    alignItems: 'center',
    flex: 1,
  },
  insightDivider: {
    width: 1,
    height: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginHorizontal: 20,
  },
  insightValue: {
    fontSize: 24,
    fontWeight: '800',
    color: 'white',
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  insightLabel: {
    marginTop: 8,
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: '#2c3e50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#ffffff',
    fontSize: 18,
  },
  notificationContainer: {
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 16,
    elevation: 2,
  },
});

export default DashboardScreen;
