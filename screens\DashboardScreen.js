import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions } from 'react-native';
import { Feather, FontAwesome, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ActivityIndicator } from 'react-native-paper';
import BottomTabNavigator from '../components/BottomTabNavigator';
import { selectQuery, monthlyExpense } from '../src/controller';
import Svg, { Circle, Path, Text as SvgText } from 'react-native-svg';

const { width } = Dimensions.get('window');

// Amazing Pie Chart Component
const PieChart = ({ data }) => {
  const colors = ['#4285F4', '#34A853', '#FBBC04', '#EA4335', '#9C27B0', '#FF9800'];
  const size = 200;
  const strokeWidth = 40;
  const radius = (size - strokeWidth) / 2;
  const center = size / 2;

  if (!data || data.length === 0) {
    return (
      <View style={styles.emptyChart}>
        <MaterialIcons name="pie-chart" size={64} color="#E0E0E0" />
        <Text style={styles.emptyChartText}>No payment data available</Text>
      </View>
    );
  }

  // Calculate angles for each slice
  let cumulativePercentage = 0;
  const slices = data.map((item, index) => {
    const percentage = parseFloat(item.percentage);
    const startAngle = cumulativePercentage * 3.6; // Convert percentage to degrees
    const endAngle = (cumulativePercentage + percentage) * 3.6;
    cumulativePercentage += percentage;

    return {
      ...item,
      startAngle,
      endAngle,
      color: colors[index % colors.length],
      percentage
    };
  });

  // Create SVG path for each slice
  const createPath = (startAngle, endAngle, radius, center) => {
    const start = polarToCartesian(center, center, radius, endAngle);
    const end = polarToCartesian(center, center, radius, startAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

    return [
      "M", center, center,
      "L", start.x, start.y,
      "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y,
      "Z"
    ].join(" ");
  };

  const polarToCartesian = (centerX, centerY, radius, angleInDegrees) => {
    const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
    return {
      x: centerX + (radius * Math.cos(angleInRadians)),
      y: centerY + (radius * Math.sin(angleInRadians))
    };
  };

  return (
    <View style={styles.chartContainer}>
      {/* SVG Pie Chart */}
      <View style={styles.pieChartWrapper}>
        <Svg width={size} height={size} style={styles.pieChart}>
          {slices.map((slice, index) => (
            <Path
              key={index}
              d={createPath(slice.startAngle, slice.endAngle, radius, center)}
              fill={slice.color}
              stroke="white"
              strokeWidth="2"
            />
          ))}
          {/* Center circle for donut effect */}
          <Circle
            cx={center}
            cy={center}
            r={radius * 0.6}
            fill="white"
            stroke="#F0F0F0"
            strokeWidth="1"
          />
          {/* Center text */}
          <SvgText
            x={center}
            y={center - 10}
            textAnchor="middle"
            fontSize="16"
            fontWeight="bold"
            fill="#333"
          >
            Total
          </SvgText>
          <SvgText
            x={center}
            y={center + 10}
            textAnchor="middle"
            fontSize="14"
            fill="#666"
          >
            ₹{data.reduce((sum, item) => sum + item.totalAmount, 0).toFixed(0)}
          </SvgText>
        </Svg>
      </View>

      {/* Legend */}
      <View style={styles.chartLegend}>
        {slices.map((slice, index) => (
          <View key={index} style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: slice.color }]} />
            <View style={styles.legendText}>
              <Text style={styles.legendMethod}>{slice.paymentMethod}</Text>
              <Text style={styles.legendAmount}>₹{slice.totalAmount.toFixed(0)} ({slice.percentage}%)</Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

const DashboardScreen = ({ navigation }) => {
  const [bankAccounts, setBankAccounts] = useState([]);
  const [cardDetails, setCardDetails] = useState([]);
  const [netBanking, setNetBanking] = useState([]);
  const [task, setTask] = useState([]);
  const [monthExpense, setMonthExpense] = useState([]);
  const [expense, setExpense] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalExpense, setTotalExpense] = useState([]);
  const [paymentMethodsData, setPaymentMethodsData] = useState([]);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const banks = await selectQuery('bank_account');
      const cards = await selectQuery('card_details');
      const netBanks = await selectQuery('net_banking');
      const tasks = await selectQuery('task');
      let year = new Date().getFullYear();
      let month = new Date().getMonth() + 1;
      console.log('Year:', year, 'Month:', month);
      const expenses = await monthlyExpense(year, month);
      const totalExpense = expenses.reduce((acc, expense) => acc + parseFloat(expense.amount), 0);
      const monthlyLimit = expenses.reduce((acc, expense) => acc + parseFloat(expense.monthlyLimit), 0);
      const grouped = expenses.reduce((acc, expense) => {
        const method = expense.paymentMethod;
        const amount = parseFloat(expense.amount) || 0;
        acc[method] = (acc[method] || 0) + amount;
        return acc;
      }, {});
      
      const paymentMethodsArray = Object.entries(grouped).map(([paymentMethod, totalAmount]) => ({
        paymentMethod,
        totalAmount,
        percentage: totalExpense > 0 ? ((totalAmount / totalExpense) * 100).toFixed(1) : 0
      }));

      setBankAccounts(banks || []);
      setCardDetails(cards || []);
      setNetBanking(netBanks || []);
      setTask(tasks || []);
      setExpense(expenses || []);
      setMonthExpense(totalExpense);
      setTotalExpense(monthlyLimit); 
      setPaymentMethodsData(paymentMethodsArray);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator animating={true} size="large" color="#3498db" />
        <Text style={styles.loadingText}>Loading Dashboard...</Text>
      </View>
    );
  }

  return (
    <>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>

        {/* Header Section with Gradient */}
        <LinearGradient
          colors={['#4285F4', '#6366F1']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <View style={styles.welcomeSection}>
              <Text style={styles.welcomeText}>Welcome back!</Text>
              <Text style={styles.welcomeSubtext}>Your secure digital wallet</Text>
            </View>
            <View style={styles.vaultIconContainer}>
              <MaterialIcons name="fingerprint" size={32} color="white" />
            </View>
          </View>

          <View style={styles.vaultStatusCard}>
            <View style={styles.vaultStatusContent}>
              <MaterialIcons name="security" size={24} color="#4285F4" />
              <View style={styles.vaultStatusText}>
                <Text style={styles.vaultStatusTitle}>Vault Status</Text>
                <Text style={styles.vaultStatusSubtitle}>Secured & Encrypted</Text>
              </View>
            </View>
          </View>
        </LinearGradient>

        <Text style={styles.sectionTitle}>Quick Access</Text>

        <View style={styles.quickAccess}>
          <TouchableOpacity style={styles.quickItem} onPress={() => navigation.navigate('Bank Account')}>
            <View style={[styles.quickItemIcon, { backgroundColor: '#E3F2FD' }]}>
              <FontAwesome name="university" size={24} color="#4285F4" />
            </View>
            <Text style={styles.quickLabel}>Bank Accounts</Text>
            <Text style={styles.quickCount}>{bankAccounts.length}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.quickItem} onPress={() => navigation.navigate('Expense Details')}>
            <View style={[styles.quickItemIcon, { backgroundColor: '#E8F5E8' }]}>
              <MaterialCommunityIcons name="account-cash" size={24} color="#4CAF50" />
            </View>
            <Text style={styles.quickLabel}>Expenses</Text>
            <Text style={styles.quickCount}>{expense.length}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.quickItem} onPress={() => navigation.navigate('Net Banking')}>
            <View style={[styles.quickItemIcon, { backgroundColor: '#FFF3E0' }]}>
              <Feather name="key" size={24} color="#FF9800" />
            </View>
            <Text style={styles.quickLabel}>Net Banking</Text>
            <Text style={styles.quickCount}>{netBanking.length}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.quickItem} onPress={() => navigation.navigate('Notes')}>
            <View style={[styles.quickItemIcon, { backgroundColor: '#F3E5F5' }]}>
              <MaterialCommunityIcons name="note-text-outline" size={24} color="#9C27B0" />
            </View>
            <Text style={styles.quickLabel}>Notes</Text>
            <Text style={styles.quickCount}>{task.length}</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Insights</Text>

        <View style={styles.insightContainer}>
          <View style={styles.insightCard}>
            <View style={styles.insightIconContainer}>
              <MaterialIcons name="trending-down" size={24} color="#FF5722" />
            </View>
            <View style={styles.insightContent}>
              <Text style={styles.insightValue}>₹{monthExpense}</Text>
              <Text style={styles.insightLabel}>Monthly Expenses</Text>
            </View>
          </View>

          <View style={styles.insightCard}>
            <View style={styles.insightIconContainer}>
              <MaterialIcons name="account-balance-wallet" size={24} color="#4285F4" />
            </View>
            <View style={styles.insightContent}>
              <Text style={styles.insightValue}>{totalExpense}</Text>
              <Text style={styles.insightLabel}>Month Limit</Text>
            </View>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Payment Methods</Text>

        <View style={styles.paymentMethodsCard}>
          <PieChart data={paymentMethodsData} />
        </View>
      </ScrollView>


      <BottomTabNavigator />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  contentContainer: {
    paddingBottom: 20,
  },

  // Header Gradient Styles
  headerGradient: {
    paddingTop: 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
    marginBottom: 0,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  welcomeSection: {
    flex: 1,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  welcomeSubtext: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '500',
  },
  vaultIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    padding: 12,
    borderRadius: 20,
  },
  vaultStatusCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  vaultStatusContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  vaultStatusText: {
    marginLeft: 12,
    flex: 1,
  },
  vaultStatusTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  vaultStatusSubtitle: {
    fontSize: 14,
    color: '#4285F4',
    fontWeight: '500',
  },

  // Section Title
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 30,
    marginBottom: 16,
    marginHorizontal: 20,
    color: '#333',
  },

  // Quick Access Styles
  quickAccess: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 20,
    marginBottom: 30,
    flexWrap: 'wrap',
  },
  quickItem: {
    width: '47%',
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 16,
    marginBottom: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  quickItemIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  quickLabel: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 4,
  },
  quickCount: {
    fontSize: 18,
    color: '#4285F4',
    fontWeight: 'bold',
  },

  // Insights Styles
  insightContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 20,
    gap: 12,
  },
  insightCard: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  insightIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#F0F4FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  insightContent: {
    flex: 1,
  },
  insightValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  insightLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },

  // Payment Methods Chart Styles
  paymentMethodsCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  chartContainer: {
    alignItems: 'center',
  },
  pieChartWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  pieChart: {
    backgroundColor: 'transparent',
  },
  chartLegend: {
    width: '100%',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  legendText: {
    flex: 1,
  },
  legendMethod: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  legendAmount: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  emptyChart: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyChartText: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    fontStyle: 'italic',
  },

  // Loading Styles
  loadingContainer: {
    flex: 1,
    backgroundColor: '#4285F4',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '500',
  },
});

export default DashboardScreen;
