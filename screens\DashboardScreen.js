import React, { useState, useEffect, use } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Feather, FontAwesome, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ActivityIndicator } from 'react-native-paper';
import BottomTabNavigator from '../components/BottomTabNavigator';
import { selectQuery, monthlyExpense } from '../src/controller';

const DashboardScreen = ({ navigation }) => {
  const [bankAccounts, setBankAccounts] = useState([]);
  const [cardDetails, setCardDetails] = useState([]);
  const [netBanking, setNetBanking] = useState([]);
  const [task, setTask] = useState([]);
  const [monthExpense, setMonthExpense] = useState([]);
  const [expense, setExpense] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalExpense, setTotalExpense] = useState(0);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const banks = await selectQuery('bank_account');
      const cards = await selectQuery('card_details');
      const netBanks = await selectQuery('net_banking');
      const tasks = await selectQuery('task');
      let year = new Date().getFullYear();
      let month = new Date().getMonth() + 1;
      const expenses = await monthlyExpense(year, month);
      const totalExpense = expenses.reduce((acc, expense) => acc + parseFloat(expense.amount), 0);
      const totalIncome = expenses.reduce((acc, expense) => acc + parseFloat(expense.remainingbalance), 0);
      setBankAccounts(banks || []);
      setCardDetails(cards || []);
      setNetBanking(netBanks || []);
      setTask(tasks || []);
      setExpense(expenses || []);
      setMonthExpense(totalExpense);
      setTotalExpense(totalIncome);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator animating={true} size="large" color="#3498db" />
        <Text style={styles.loadingText}>Loading Dashboard...</Text>
      </View>
    );
  }

  return (
    <>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>

        <View style={styles.vaultContainer}>
          <View style={styles.vaultContent}>
            <Text style={styles.vaultMonth}>January</Text>
            <Text style={styles.vaultAmount}>₹ {totalExpense}</Text>
            <Text style={styles.vaultTarget}>Daily spend target: ₹{Math.round(monthExpense/30)}</Text>
            <View style={styles.progressContainer}>
              <Text style={styles.progressText}>70%</Text>
            </View>
          </View>
        </View>

        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>DAILY SPEND</Text>
          <TouchableOpacity>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.quickAccess}>
          <TouchableOpacity onPress={() => navigation.navigate('Bank Account')} style={styles.quickItem}>
            <View style={[styles.quickIconContainer, { backgroundColor: '#FF5252' }]}>
              <FontAwesome name="university" size={24} color="white" />
            </View>
            <Text style={styles.quickLabel}>Net Banking</Text>
            <Text style={styles.quickAmount}>₹{bankAccounts.length * 365}.89</Text>
            <Text style={styles.quickDate}>Today</Text>
          </TouchableOpacity>

          <TouchableOpacity onPress={() => navigation.navigate('Expense')} style={styles.quickItem}>
            <View style={[styles.quickIconContainer, { backgroundColor: '#FF9800' }]}>
              <MaterialCommunityIcons name="food" size={24} color="white" />
            </View>
            <Text style={styles.quickLabel}>Food & Drinks</Text>
            <Text style={styles.quickAmount}>₹{expense.length * 165}.90</Text>
            <Text style={styles.quickDate}>20 Jan 2019</Text>
          </TouchableOpacity>

          <TouchableOpacity onPress={() => navigation.navigate('Net Banking')} style={styles.quickItem}>
            <View style={[styles.quickIconContainer, { backgroundColor: '#4CAF50' }]}>
              <MaterialCommunityIcons name="tshirt-crew" size={24} color="white" />
            </View>
            <Text style={styles.quickLabel}>Clothes</Text>
            <Text style={styles.quickAmount}>₹{netBanking.length * 65}.09</Text>
            <Text style={styles.quickDate}>19 Jan 2019</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>WISHLIST</Text>
          <TouchableOpacity>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.wishlistContainer}>
          <TouchableOpacity style={[styles.wishlistItem, { backgroundColor: '#4285F4' }]}>
            <MaterialCommunityIcons name="tea" size={32} color="white" />
            <Text style={styles.wishlistText}>Tea</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.wishlistItem, { backgroundColor: '#4CAF50' }]}>
            <MaterialCommunityIcons name="dumbbell" size={32} color="white" />
            <Text style={styles.wishlistText}>Gym</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.wishlistItem, { backgroundColor: '#FF9800' }]}>
            <MaterialCommunityIcons name="car" size={32} color="white" />
            <Text style={styles.wishlistText}>Car</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.wishlistItem, { backgroundColor: '#FF5252' }]}>
            <MaterialCommunityIcons name="account-group" size={32} color="white" />
            <Text style={styles.wishlistText}>Saving</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.wishlistItem, { backgroundColor: '#2196F3' }]}>
            <MaterialCommunityIcons name="plus" size={32} color="white" />
          </TouchableOpacity>
        </View>
      </ScrollView>


      <BottomTabNavigator />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 100,
  },
  vaultContainer: {
    backgroundColor: '#4285F4',
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#4285F4',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  vaultContent: {
    flex: 1,
  },
  vaultMonth: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  vaultAmount: {
    color: 'white',
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  vaultTarget: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginBottom: 16,
  },
  progressContainer: {
    alignSelf: 'flex-end',
  },
  progressText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  vaultText: {
    fontSize: 28,
    fontWeight: '800',
    color: 'white',
    letterSpacing: 1,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  vaultSubtext: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '600',
    marginTop: 4,
    letterSpacing: 0.5,
  },
  vaultIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    padding: 16,
    borderRadius: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#9E9E9E',
    letterSpacing: 1,
  },
  seeAllText: {
    fontSize: 12,
    color: '#4285F4',
    fontWeight: '600',
  },
  quickAccess: {
    marginBottom: 24,
  },
  quickItem: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  quickLabel: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
    flex: 1,
  },
  quickAmount: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
    marginRight: 8,
  },
  quickDate: {
    fontSize: 12,
    color: '#9E9E9E',
    fontWeight: '400',
  },
  insightContainer: {
    padding: 24,
    borderRadius: 24,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#6366f1',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 12,
    overflow: 'hidden',
  },
  insightBox: {
    alignItems: 'center',
    flex: 1,
  },
  insightDivider: {
    width: 1,
    height: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginHorizontal: 20,
  },
  insightIcon: {
    fontSize: 32,
    marginBottom: 8,
    textAlign: 'center',
  },
  insightValue: {
    fontSize: 26,
    fontWeight: '800',
    color: 'white',
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  insightLabel: {
    marginTop: 8,
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  wishlistContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  wishlistItem: {
    width: 60,
    height: 60,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  wishlistText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
    marginTop: 4,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#333',
    fontSize: 18,
  },
});

export default DashboardScreen;
