{"name": "LAKSHCRYPT", "license": "0BSD", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@boltcode/react-native-sqlite-storage": "^7.0.0", "@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-ml-kit/text-recognition": "^1.5.2", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.1.1", "axios": "^1.4.0", "base-64": "^1.0.0", "expo": "53.0.9", "expo-blur": "~14.1.4", "expo-camera": "~16.1.6", "expo-crypto": "~14.1.4", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.9", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-local-authentication": "~16.0.4", "expo-sharing": "~13.1.5", "expo-sqlite": "~15.2.9", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-autocomplete-input": "^5.5.6", "react-native-awesome-alerts": "^2.0.0", "react-native-draggable-flatlist": "^4.0.1", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0", "react-native-vision-camera": "^4.6.4", "react-native-web": "^0.20.0", "react-native-webview": "^13.13.5"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}