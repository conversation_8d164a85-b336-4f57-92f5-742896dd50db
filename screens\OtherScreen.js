import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, ScrollView, Alert, Modal, Share } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { MaterialIcons, Feather } from '@expo/vector-icons';
import BottomTabNavigator from '../components/BottomTabNavigator';
import DateTimePicker from '@react-native-community/datetimepicker';
import { insertQuery, selectQuery } from '../src/controller';
import { encrypt, decrypt } from '../src/utils';

const OtherScreen = ({ navigation }) => {
  const [documentType, setDocumentType] = useState('SELECT');
  const [selectedUid, setSelectedUid] = useState('');
  const [formFields, setFormFields] = useState([]);
  const [formData, setFormData] = useState({});
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [otherData, setOtherData] = useState({});
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [currentDateField, setCurrentDateField] = useState('');

  // Filter only active forms
  const activeFormFields = formFields.filter(field => field.active === 1);

  useEffect(() => {
    const loadData = async () => {
      try {
        await fetchData();
      } catch (error) {
        console.error('Initial load error:', error);
        // Initialize with empty state if loading fails
        setOtherData({});
        setFormFields([]);
      }
    };

    loadData();
  }, []);

  const fetchData = async () => {
    try {
      // Fetch form definitions
      const forms = await selectQuery('custom_form', {}, '*', { orderType: 'title' });
      const processedForms = forms.map(item => ({
        ...item,
        title: item.title || 'Untitled Form',
        form_inputs: item.form_inputs ? JSON.parse(item.form_inputs) : [],
        active: item.active,
        isReminder: Boolean(item.isReminder)
      }));

      setFormFields(processedForms);

      // Fetch form data with robust error handling
      const formData = await selectQuery('custom_form_data', {}, '*');

      const processedData = await Promise.all(
        formData.map(async (item) => {
          try {
            let details = {};

            if (item.details) {
              try {
                // Initialize details outside to maintain scope
                let decryptedDetails = {};

                // Try to parse the original string to an object first
                let parsedDetails = item.details;
                if (typeof item.details === 'string') {
                  parsedDetails = JSON.parse(item.details);
                }

                // Decrypt each field
                for (const key in parsedDetails) {
                  const value = parsedDetails[key];
                  console.log(`Decrypting ${key}: ${value}`);
                  if (typeof value === 'string') {
                    decryptedDetails[key] = await decrypt(value);
                  } else {
                    decryptedDetails[key] = value;
                  }
                }

                details = decryptedDetails;
              } catch (decryptError) {
                console.warn('Decryption failed, trying plain JSON:', decryptError);
                try {
                  details = JSON.parse(item.details) || {};
                } catch (parseError) {
                  console.warn('JSON parse failed:', parseError);
                }
              }
            }

            return {
              ...item,
              type: item.type || 'unknown',
              details: details,
              custom_form_uid: item.custom_form_uid || null
            };
          } catch (error) {
            console.error('Item processing error:', error);
            return {
              ...item,
              type: item.type || 'unknown',
              details: {},
              custom_form_uid: item.custom_form_uid || null
            };
          }
        })
      );

      // Format data with null checks
      const formattedData = processedData.reduce((acc, item) => {
        if (!item || !item.type) return acc;

        if (!acc[item.type]) acc[item.type] = [];
        if (item.details && typeof item.details === 'object') {
          acc[item.type].push(item.details);
        }
        return acc;
      }, {});

      setOtherData(formattedData);
    } catch (error) {
      console.error('Fetch error:', error);
      setOtherData({});
      Alert.alert('Error', 'Failed to load data. Please try again.');
    }
  };

  const handleInputChange = (key, value) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };

  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      handleInputChange(currentDateField, selectedDate.toISOString().split('T')[0]);
    }
  };

  const handleSave = async () => {
    try {

      if (!selectQuery || !formData || Object.keys(formData).length === 0) {
        Alert.alert('Validation Error', 'Please fill in the form!');
        return;
      }

      const encryptedData = {};
      for (const key in formData) {
        const value = formData[key];
        console.log(`Encrypting ${key}: ${value}`);
        if (typeof value === 'string') {
          encryptedData[key] = await encrypt(value);
        } else {
          encryptedData[key] = value;
        }
      }

      const jsonString = JSON.stringify(encryptedData);
      if (!jsonString) {
        throw new Error('Failed to convert encrypted data to JSON');
      }

      // Insert into database
      await insertQuery('custom_form_data', {
        type: documentType,
        details: jsonString,
        custom_form_uid: selectedUid
      });

      Alert.alert('Success', 'Document saved successfully!');
    } catch (error) {
      console.error('Save error:', error);
      Alert.alert('Error', 'Failed to save document: ' + error.message);
    }
  };


  const formatLabel = (label) => {
    return label
      .replace(/([A-Z])/g, " $1")
      .replace(/_/g, " ")
      .replace(/\b\w/g, char => char.toUpperCase());
  };

  const handleShare = async (entry) => {
    try {
      const message = Object.entries(entry)
        .map(([key, value]) => `${formatLabel(key)}: ${value}`)
        .join('\n');

      await Share.share({
        message: `Document Details:\n\n${message}`,
      });
    } catch (error) {
      console.error('Share error:', error);
      Alert.alert('Error', 'Unable to share');
    }
  };

  const renderInputField = (input) => {
    switch (input.inputType) {
      case 'NUMBER':
        return (
          <TextInput
            style={styles.input}
            placeholder={`Enter ${input.label}`}
            value={formData[input.key] || ''}
            onChangeText={text => handleInputChange(input.key, text)}
            keyboardType="numeric"
          />
        );
      case 'DATE':
        return (
          <TouchableOpacity
            style={styles.input}
            onPress={() => {
              setCurrentDateField(input.key);
              setShowDatePicker(true);
            }}
          >
            <Text>{formData[input.key] || `Select ${input.label}`}</Text>
          </TouchableOpacity>
        );
      default:
        return (
          <TextInput
            style={styles.input}
            placeholder={`Enter ${input.label}`}
            value={formData[input.key] || ''}
            onChangeText={text => handleInputChange(input.key, text)}
          />
        );
    }
  };

  return (
    <>
      <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollView}>
  {Object.keys(otherData).length === 0 ? (
    <Text style={styles.emptyText}>No data available</Text>
  ) : (
    Object.entries(otherData).map(([type, entries]) => (
      <View key={type} style={styles.card}>
        <View style={styles.cardHeader}>
          <Feather name="file-text" size={22} color="#fff" />
          <Text style={styles.cardTitle}>{formatLabel(type)}</Text>
          <TouchableOpacity
              style={styles.shareButton}
              onPress={() => handleShare(entry)}
            >
              <Feather name="share-2" size={20} color="#ffffff" />
            </TouchableOpacity>
        </View>
        {entries.map((entry, index) => (
          <View key={index} style={styles.entryWrapper}>
            {Object.entries(entry).map(([key, value]) => (
              <View key={key} style={styles.entryRow}>
                <Text style={styles.entryLabel}>{formatLabel(key)}:</Text>
                <Text style={styles.entryValue}>
                  {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                </Text>
              </View>
            ))}
          </View>
        ))}
      </View>
    ))
  )}
</ScrollView>


        <TouchableOpacity
          style={styles.fab}
          onPress={() => setIsFormVisible(true)}
        >
          <Text style={styles.fabText}>+</Text>
        </TouchableOpacity>

        <Modal visible={isFormVisible} animationType="slide" transparent>
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <ScrollView contentContainerStyle={styles.scrollContainer}>
                <Text style={styles.headerText}>Document Details</Text>

                <View style={styles.inputContainer}>
                  <Text style={styles.label}>Document Type</Text>
                  <View style={styles.pickerContainer}>
                    <Picker
                      selectedValue={documentType}
                      onValueChange={(value) => {
                        setDocumentType(value);
                        setFormData({});
                        const form = activeFormFields.find(f => f.title === value);
                        setSelectedUid(form?.uid || '');
                      }}
                      style={styles.picker}
                    >
                      <Picker.Item label="Select Document Type" value="SELECT" />
                      {activeFormFields.map(form => (
                        <Picker.Item
                          key={form.uid}
                          label={form.title}
                          value={form.title}
                        />
                      ))}
                    </Picker>
                  </View>
                </View>

                {documentType !== 'SELECT' &&
                  activeFormFields
                    .find(form => form.title === documentType)
                    ?.form_inputs.map(input => (
                      <View key={input.key} style={styles.inputContainer}>
                        <Text style={styles.label}>{input.label}</Text>
                        {renderInputField(input)}
                      </View>
                    ))
                }

                <View style={styles.buttonRow}>
                  <TouchableOpacity
                    style={styles.saveButton}
                    onPress={handleSave}
                  >
                    <Text style={styles.saveButtonText}>Save</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => setIsFormVisible(false)}
                  >
                    <Text style={styles.closeButtonText}>Close</Text>
                  </TouchableOpacity>
                </View>
              </ScrollView>
            </View>
          </View>
        </Modal>

        {showDatePicker && (
          <DateTimePicker
            value={new Date()}
            mode="date"
            display="default"
            onChange={handleDateChange}
          />
        )}
      </View>
      <BottomTabNavigator menu={'Other'} />
    </>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20, backgroundColor: '#fff' },
  headerText: { fontSize: 22, fontWeight: 'bold', textAlign: 'center', marginBottom: 20 },
  emptyText: { textAlign: 'center', color: '#888', fontSize: 16 },
  section: { marginBottom: 20 },
  sectionTitle: { fontSize: 18, fontWeight: 'bold', color: '#2c3e50', marginBottom: 5 },
  entry: { backgroundColor: '#f4f4f4', padding: 10, borderRadius: 8, marginBottom: 5, position: 'relative' },
  entryText: { fontSize: 16, color: '#333' },
  bold: { fontWeight: 'bold' },
  fab: { position: 'absolute', bottom: '15%', right: 20, width: 60, height: 60, backgroundColor: '#2c3e50', borderRadius: 30, justifyContent: 'center', alignItems: 'center', elevation: 5 },
  fabText: { fontSize: 30, color: '#fff', fontWeight: 'bold' },
  modalContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0, 0, 0, 0.5)' },
  modalContent: { width: '90%', backgroundColor: '#fff', padding: 20, borderRadius: 15, elevation: 5, maxHeight: '80%' },
  inputContainer: { marginBottom: 15 },
  label: { fontSize: 16, fontWeight: '500', marginBottom: 5 },
  input: {
    height: 45,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 10,
    justifyContent: 'center',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    overflow: 'hidden',
  },
  picker: { height: 50, width: '100%' },
  buttonRow: { flexDirection: 'row', justifyContent: 'space-between', marginTop: 20 },
  saveButton: { flex: 1, backgroundColor: '#2c3e50', padding: 10, borderRadius: 8, alignItems: 'center', marginRight: 4 },
  saveButtonText: { color: '#fff', fontSize: 16 },
  closeButton: { flex: 1, backgroundColor: '#d9534f', padding: 10, borderRadius: 8, alignItems: 'center' },
  closeButtonText: { color: '#fff', fontSize: 16 },
  scrollView: { padding: 15 },
  emptyText: { textAlign: 'center', fontSize: 18, color: 'gray', marginTop: 20 },
  card: { backgroundColor: 'white', borderRadius: 12, marginBottom: 15, elevation: 4, overflow: 'hidden' },
  cardHeader: { flexDirection: 'row', alignItems: 'center', backgroundColor: '#34495e', padding: 12, borderTopLeftRadius: 12, borderTopRightRadius: 12 },
  cardTitle: { fontSize: 18, fontWeight: 'bold', color: 'white', marginLeft: 10, flex: 1 },
  entryWrapper: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 14,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
    borderRadius: 12,
    marginVertical: 8,
    position: 'relative',
  },
  entryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  entryLabel: {
    fontWeight: '600',
    fontSize: 16,
    color: '#2c3e50',
    flex: 1,
  },
  entryValue: {
    fontSize: 16,
    color: '#34495e',
    flex: 1,
    textAlign: 'right',
  },
  shareButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    padding: 6,
    borderRadius: 6,
  },
});

export default OtherScreen;