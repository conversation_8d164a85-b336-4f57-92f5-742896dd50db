import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Share,
  ScrollView,
} from "react-native";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";

const ServiceCardDetails = ({ selectedItem, showDataHeader, isShare, title }) => {
  const [showSensitiveInfo, setShowSensitiveInfo] = useState(false);
  if (!selectedItem) return null;

  const toggleSensitiveInfo = () => setShowSensitiveInfo(!showSensitiveInfo);

  const handleShare = async () => {
    try {
      let shareMessage = "";
      showDataHeader?.forEach((field) => {
        if (field.isVisible === 1 && field.headerKey !== "password") {
          shareMessage += `${field.headerValue}: ${selectedItem[field.headerKey] || "N/A"}\n`;
        }
      });
      await Share.share({ message: shareMessage });
    } catch (error) {
      console.log("Error sharing:", error);
    }
  };

  return (
    <View style={styles.wrapper}>
      {/* Beautiful main container */}
      <View style={styles.mainContainer}>

        {/* Header Card */}
        <LinearGradient
          colors={['#4285F4', '#6366F1']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerCard}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerInfo}>
              <View style={styles.iconWrapper}>
                <MaterialIcons name="account-circle" size={28} color="white" />
              </View>
              <View style={styles.titleSection}>
                <Text style={styles.cardTitle}>{title}</Text>
                <Text style={styles.cardSubtitle}>Account Details</Text>
              </View>
            </View>

            <View style={styles.headerActions}>
              <TouchableOpacity onPress={toggleSensitiveInfo} style={styles.actionButton}>
                <Ionicons name={showSensitiveInfo ? "eye-off" : "eye"} size={20} color="white" />
              </TouchableOpacity>
              {isShare === 1 && (
                <TouchableOpacity onPress={handleShare} style={styles.actionButton}>
                  <Ionicons name="share-social" size={20} color="white" />
                </TouchableOpacity>
              )}
            </View>
          </View>
        </LinearGradient>

        {/* Individual Field Cards */}
        <ScrollView style={styles.fieldsContainer} showsVerticalScrollIndicator={false}>
          {showDataHeader?.map((field, index) =>
            field.isVisible === 1 ? (
              <View key={index} style={styles.fieldCard}>
                {/* Field Header */}
                <View style={styles.fieldCardHeader}>
                  <View style={styles.fieldIconContainer}>
                    <MaterialIcons
                      name={getFieldIcon(field.headerKey)}
                      size={20}
                      color="#4285F4"
                    />
                  </View>
                  <Text style={styles.fieldLabel}>{field.headerValue}</Text>
                  {(field.headerKey.toLowerCase().includes("password") ||
                    field.headerKey.toLowerCase().includes("pin")) && (
                    <View style={styles.securityBadge}>
                      <MaterialIcons name="security" size={12} color="#FF5722" />
                      <Text style={styles.securityText}>Secure</Text>
                    </View>
                  )}
                </View>

                {/* Field Value */}
                <View style={styles.fieldValueContainer}>
                  <Text style={styles.fieldValue}>
                    {showSensitiveInfo
                      ? selectedItem[field.headerKey] || "N/A"
                      : field.headerKey.toLowerCase().includes("password") ||
                        field.headerKey.toLowerCase().includes("pin")
                      ? "••••••••••••"
                      : selectedItem[field.headerKey] || "N/A"}
                  </Text>

                  {/* Copy Button */}
                  <TouchableOpacity style={styles.copyButton}>
                    <MaterialIcons name="content-copy" size={16} color="#4285F4" />
                  </TouchableOpacity>
                </View>
              </View>
            ) : null
          )}

          {/* Bottom Spacing */}
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </View>
    </View>
  );
};

// Helper function to get appropriate icon for field
const getFieldIcon = (fieldKey) => {
  const key = fieldKey.toLowerCase();
  if (key.includes('email')) return 'email';
  if (key.includes('password') || key.includes('pin')) return 'lock';
  if (key.includes('phone') || key.includes('mobile')) return 'phone';
  if (key.includes('account') || key.includes('number')) return 'account-balance';
  if (key.includes('name') || key.includes('user')) return 'person';
  if (key.includes('bank')) return 'account-balance-wallet';
  if (key.includes('url') || key.includes('website')) return 'language';
  return 'info';
};

const styles = StyleSheet.create({
  wrapper: {
    padding: 16,
    alignItems: "center",
    justifyContent: "flex-start",
    width: "100%",
    backgroundColor: "#F5F5F5",
  },

  // Main Container
  mainContainer: {
    width: "100%",
    maxHeight: 500,
  },

  // Header Card Styles
  headerCard: {
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
  },
  headerInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  iconWrapper: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  titleSection: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "white",
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.8)",
    fontWeight: "500",
  },
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
  },

  // Fields Container
  fieldsContainer: {
    flex: 1,
  },

  // Individual Field Card Styles
  fieldCard: {
    backgroundColor: "white",
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    overflow: "hidden",
  },
  fieldCardHeader: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "#F8F9FA",
    borderBottomWidth: 1,
    borderBottomColor: "#E9ECEF",
  },
  fieldIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#E3F2FD",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
    flex: 1,
    textTransform: "capitalize",
  },
  securityBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFEBEE",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  securityText: {
    fontSize: 10,
    fontWeight: "600",
    color: "#FF5722",
    marginLeft: 4,
  },

  // Field Value Styles
  fieldValueContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  fieldValue: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
    flex: 1,
    marginRight: 12,
  },
  copyButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#F0F4FF",
    justifyContent: "center",
    alignItems: "center",
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 20,
  },
});

export default ServiceCardDetails;
