import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, StyleSheet, ActivityIndicator, Dimensions, Modal, TouchableOpacity, Share } from 'react-native';
import { Card, Provider as <PERSON><PERSON>rovider, Button } from 'react-native-paper';
import { MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { selectQuery } from '../src/controller';
import CreditCardDetails from './CreditCardDetails';
import ServiceCardDetails from './ServiceCardDetails';

const screenWidth = Dimensions.get('window').width;

// Updated color scheme to match DashboardScreen
const subjectColors = ['#4285F4', '#34A853', '#FBBC04', '#EA4335', '#9C27B0', '#FF9800'];
const subjectIcons = {
  'Bank Account': 'bank',
  'Credit Card': 'credit-card',
  'Debit Card': 'credit-card-outline',
  'Net Banking': 'desktop-mac',
  'Demat': 'chart-line',
  'Card Details': 'credit-card',
  'Email': 'email',
  'App Details': 'application',
};

const ShowList = ({ title, tableKey, data }) => {
  console.log('ShowList:', title, data, tableKey);
  const [listSetting, setListSetting] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedItem, setSelectedItem] = useState(null);

  useEffect(() => {
    const initSetting = async () => {
      setLoading(true);
      try {
        //let sync = await getFilteredConfig('title', title);
        let sync = await selectQuery('config', {
          table_key: {
            value: tableKey,
            filter: 'equal',
            dataType: 'text',
          }
        }, '*', { orderBy: 'title' });
        if (sync?.length > 0) {
          sync = sync.map(sc => ({
            ...sc,
            mainHeader: JSON.parse(sc.mainHeader || '[]'),
            showDataHeader: JSON.parse(sc.showDataHeader || '[]'),
          }));
          setListSetting(sync);
        }
      } catch (error) {
        console.error("Error loading settings:", error);
      }
      setLoading(false);
    };
    initSetting();
  }, [title]);


  const listConfig = listSetting.find(item => item.table_key === tableKey);

  if (loading) return <ActivityIndicator size="large" color="#6200ee" style={styles.loading} />;
  if (!listConfig) return <Text style={styles.errorText}>Invalid List Setting</Text>;

  const { mainHeader, showDataHeader, isShare, isVisible } = listConfig;
  console.log('List Config:', mainHeader);
  const handleShare = async (item) => {
    let shareText = showDataHeader
      .filter(field => field.isVisible === 1)
      .map(field => `${field.headerValue}: ${item[field.headerKey] || 'N/A'}`)
      .join('\n');

    try {
      await Share.share({ message: shareText });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const renderItem = ({ item, index }) => {
    const color = subjectColors[index % subjectColors.length];
    const icon = subjectIcons[title] || 'bank';

    return (
      <TouchableOpacity
        onPress={() => setSelectedItem(item)}
        style={styles.cardTouchable}
        activeOpacity={0.7}
      >
        <View style={styles.card}>
          <View style={styles.cardContent}>
            <View style={[styles.iconContainer, { backgroundColor: color }]}>
              <MaterialCommunityIcons name={icon} size={24} color="white" />
            </View>
            <View style={styles.textContainer}>
              {mainHeader.length > 0 && (
                <>
                  <Text style={styles.mainText}>{item[mainHeader[0]?.headerValue]}</Text>
                  <Text style={styles.subText}>{item[mainHeader[1]?.headerValue] || 'N/A'}</Text>
                </>
              )}
            </View>
            <View style={styles.arrowContainer}>
              <MaterialIcons name="arrow-forward-ios" size={16} color="#666" />
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <PaperProvider>
      <View style={styles.container}>
        {/* Header with gradient background */}
        <LinearGradient
          colors={['#4285F4', '#6366F1']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <Text style={styles.header}>{title}</Text>
          <Text style={styles.headerSubtitle}>{data.length} items</Text>
        </LinearGradient>

        {isVisible === 1 ? (
          <FlatList
            data={data}
            keyExtractor={(item, index) => index.toString()}
            renderItem={renderItem}
            contentContainerStyle={{ paddingBottom: 20 }}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.restrictionContainer}>
            <MaterialIcons name="visibility-off" size={48} color="#FF9800" />
            <Text style={styles.restrictionTitle}>Access Restricted</Text>
            <Text style={styles.restrictionText}>Data viewing is currently disabled for this section</Text>
          </View>
        )}

        {/* Modal for details view */}
        {selectedItem && (
          <Modal visible={!!selectedItem} transparent animationType="fade">
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                {title === "Card Details" ? (
                  <CreditCardDetails cardData={selectedItem} />
                ) : (
                  <ServiceCardDetails
                    selectedItem={selectedItem}
                    showDataHeader={showDataHeader}
                    isShare={isShare}
                    title={title}
                  />
                )}
              </View>

              {/* Close Button */}
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setSelectedItem(null)}
              >
                <MaterialCommunityIcons name="close-circle" size={30} color="white" />
              </TouchableOpacity>
            </View>
          </Modal>
        )}
      </View>
    </PaperProvider>
  );

};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    width: screenWidth,
    alignSelf: 'center',
  },

  // Header Gradient Styles
  headerGradient: {
    paddingTop: 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    color: 'white',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '500',
  },

  // Card Styles
  cardTouchable: {
    marginHorizontal: 20,
    marginBottom: 12,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  textContainer: {
    flex: 1,
  },
  mainText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  subText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  arrowContainer: {
    padding: 8,
  },

  // Restriction Styles
  restrictionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  restrictionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  restrictionText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },

  // Modal Styles
  modalOverlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.6)",
  },
  modalContent: {
    width: "90%",
    backgroundColor: "transparent",
    justifyContent: "center",
    alignItems: "center",
  },
  closeButton: {
    marginTop: 20,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    borderRadius: 30,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },

  // Loading Styles
  loading: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
  },
});

export default ShowList;
